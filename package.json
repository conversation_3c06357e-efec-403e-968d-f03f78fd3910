{"name": "pet-adoption-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start --port 3000", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "setup": "./scripts/setup-dev.sh", "port-cleanup": "./scripts/port-cleanup.sh"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@stripe/stripe-js": "^4.8.0", "@tanstack/react-query": "^5.59.20", "@types/lodash": "^4.17.17", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "critters": "^0.0.23", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.1", "framer-motion": "^11.11.17", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "next": "15.3.3", "next-auth": "^4.24.10", "nodemailer": "^6.9.16", "pdf-lib": "^1.17.1", "prisma": "^5.22.0", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.58.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.13.1", "resend": "^4.0.1", "sonner": "^1.7.0", "stripe": "^17.3.1", "tailwind-merge": "^2.5.4", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.2", "typescript": "^5"}}