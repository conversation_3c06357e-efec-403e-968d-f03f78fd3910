# 🔐 Admin Dashboard Access Guide

## Quick Access

To access the admin dashboard at `http://localhost:3000/admin`, you need to be logged in as an admin user.

### Default Admin Credentials

**Email:** `<EMAIL>`  
**Password:** `admin123`

### Steps to Access Admin Dashboard

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Open the sign-in page:**
   ```
   http://localhost:3000/auth/signin
   ```

3. **Sign in with admin credentials:**
   - Email: `<EMAIL>`
   - Password: `admin123`

4. **Navigate to admin dashboard:**
   ```
   http://localhost:3000/admin
   ```

### Other Test Users

**Staff User:**
- Email: `<EMAIL>`
- Password: `staff123`

**Volunteer User:**
- Email: `<EMAIL>`
- Password: `volunteer123`

### Creating Additional Admin Users

Run the admin creation script:
```bash
npx tsx scripts/create-admin.ts
```

### Troubleshooting

If you see "Failed to load dashboard data":

1. **Check if you're logged in** - The dashboard requires admin authentication
2. **Verify your role** - Only ADMIN and STAFF users can access the dashboard
3. **Check the browser console** - Look for detailed error messages
4. **Verify database connection** - Ensure PostgreSQL is running

### Database Status

The dashboard queries these tables:
- ✅ pets: Available
- ✅ applications: Available  
- ✅ volunteerProfiles: Available
- ✅ fosterProfiles: Available

All database tables are properly configured and accessible.

### Security Notes

- Admin credentials are for development only
- Change default passwords in production
- Admin access is protected by role-based authentication
- All API endpoints require proper authentication
