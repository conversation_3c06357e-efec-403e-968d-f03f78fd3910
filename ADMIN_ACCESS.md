# 🔐 Admin Dashboard Access Guide

## 🚀 Professional Admin Access Routes

### **Primary Admin Portal** (Recommended)
```
http://localhost:3000/admin-portal
```
Professional landing page with secure access to admin dashboard

### **Direct Admin Login** (For Bookmarking)
```
http://localhost:3000/admin/login
```
Dedicated admin authentication page - separate from public login

### **Admin Dashboard** (After Authentication)
```
http://localhost:3000/admin
```
Main administrative interface (requires authentication)

## Default Admin Credentials

**Email:** `<EMAIL>`
**Password:** `admin123`

## Quick Access Steps

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Access the admin portal:**
   ```
   http://localhost:3000/admin-portal
   ```

3. **Click "Access Admin Dashboard"** - you'll be redirected to the secure login

4. **Sign in with admin credentials:**
   - Email: `<EMAIL>`
   - Password: `admin123`

5. **You'll be automatically redirected to the admin dashboard**

### Other Test Users

**Staff User:**
- Email: `<EMAIL>`
- Password: `staff123`

**Volunteer User:**
- Email: `<EMAIL>`
- Password: `volunteer123`

### Creating Additional Admin Users

Run the admin creation script:
```bash
npx tsx scripts/create-admin.ts
```

### Troubleshooting

If you see "Failed to load dashboard data":

1. **Check if you're logged in** - The dashboard requires admin authentication
2. **Verify your role** - Only ADMIN and STAFF users can access the dashboard
3. **Check the browser console** - Look for detailed error messages
4. **Verify database connection** - Ensure PostgreSQL is running

### Database Status

The dashboard queries these tables:
- ✅ pets: Available
- ✅ applications: Available  
- ✅ volunteerProfiles: Available
- ✅ fosterProfiles: Available

All database tables are properly configured and accessible.

### Security Notes

- Admin credentials are for development only
- Change default passwords in production
- Admin access is protected by role-based authentication
- All API endpoints require proper authentication
