#!/bin/bash

# Port cleanup script for Pet Adoption Website
echo "🧹 Cleaning up ports for Pet Adoption Website..."

# Function to kill processes on specific ports
kill_port() {
    local port=$1
    echo "Checking port $port..."
    
    # Find processes using the port
    local pids=$(lsof -ti :$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "Found processes on port $port: $pids"
        echo "Killing processes..."
        kill -9 $pids 2>/dev/null
        echo "✅ Processes on port $port killed"
    else
        echo "✅ Port $port is already free"
    fi
}

# Kill processes on ports 3001, 3002, 3003
kill_port 3001
kill_port 3002
kill_port 3003

# Check if port 3000 is available
echo "Checking if port 3000 is available..."
if lsof -i :3000 >/dev/null 2>&1; then
    echo "⚠️  Port 3000 is in use. Do you want to kill the process? (y/N)"
    read -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        kill_port 3000
    else
        echo "❌ Port 3000 is still in use. Please stop the process manually."
        exit 1
    fi
else
    echo "✅ Port 3000 is available"
fi

echo ""
echo "🎉 Port cleanup complete!"
echo "All ports are now ready for the Pet Adoption Website to run on port 3000"
echo ""
echo "You can now start the development server with:"
echo "npm run dev"
