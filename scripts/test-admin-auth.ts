import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function testAdminAuth() {
  try {
    console.log('Testing admin authentication...')

    // Check if admin user exists
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        password: true,
      }
    })

    if (!admin) {
      console.log('❌ Admin user not found')
      return
    }

    console.log('✅ Admin user found:', {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      status: admin.status,
      hasPassword: !!admin.password
    })

    // Test password verification
    if (admin.password) {
      const isPasswordValid = await bcrypt.compare('admin123', admin.password)
      console.log('✅ Password verification:', isPasswordValid ? 'VALID' : 'INVALID')
    } else {
      console.log('⚠️  No password set for admin user')
    }

    // Check database tables that dashboard queries
    const tableChecks = await Promise.all([
      prisma.pet.count().then(count => ({ table: 'pets', count, error: null })).catch(error => ({ table: 'pets', count: 0, error: error.message })),
      prisma.application.count().then(count => ({ table: 'applications', count, error: null })).catch(error => ({ table: 'applications', count: 0, error: error.message })),
      prisma.volunteerProfile.count().then(count => ({ table: 'volunteerProfiles', count, error: null })).catch(error => ({ table: 'volunteerProfiles', count: 0, error: error.message })),
      prisma.fosterProfile.count().then(count => ({ table: 'fosterProfiles', count, error: null })).catch(error => ({ table: 'fosterProfiles', count: 0, error: error.message })),
    ])

    console.log('\n📊 Database table status:')
    tableChecks.forEach(({ table, count, error }) => {
      if (error) {
        console.log(`❌ ${table}: ERROR - ${error}`)
      } else {
        console.log(`✅ ${table}: ${count} records`)
      }
    })

  } catch (error) {
    console.error('❌ Error testing admin auth:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAdminAuth()
