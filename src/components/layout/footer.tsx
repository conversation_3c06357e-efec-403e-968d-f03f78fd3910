"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Phone,
  MapPin,
  Send,
  Sparkles,
  PawPrint,
  Users,
  Gift,
  CheckCircle
} from "lucide-react"
import { cn } from "@/lib/utils"

// Sample adopted pets data for the live feed
const adoptedPetsData = [
  {
    id: 1,
    name: "<PERSON>",
    adopter: "<PERSON> & <PERSON>",
    image: "🐕",
    message: "<PERSON> learned to fetch today! She's such a smart girl! 🎾",
    timeAgo: "2 hours ago",
    location: "Seattle, WA"
  },
  {
    id: 2,
    name: "<PERSON>his<PERSON>",
    adopter: "<PERSON>",
    image: "🐱",
    message: "First vet checkup went perfectly! Whiskers is healthy and happy! 💚",
    timeAgo: "5 hours ago",
    location: "Austin, TX"
  },
  {
    id: 3,
    name: "<PERSON>",
    adopter: "<PERSON>",
    image: "🐶",
    message: "<PERSON> made a new friend at the dog park today! 🐕‍🦺",
    timeAgo: "1 day ago",
    location: "Denver, CO"
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>",
    adopter: "<PERSON>",
    image: "🐈",
    message: "Mittens discovered the joy of cardboard boxes! 📦",
    timeAgo: "2 days ago",
    location: "Boston, MA"
  }
]

// Paw-shaped divider component
function PawDivider() {
  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.8, type: "spring" }}
      className="flex items-center gap-4"
    >
      <div className="h-px bg-gradient-to-r from-transparent to-white/30 w-16" />
      <motion.div
        animate={{ rotate: [0, 10, -10, 0] }}
        transition={{ duration: 3, repeat: Infinity }}
        className="text-yellow-400"
      >
        <PawPrint className="h-8 w-8" />
      </motion.div>
      <div className="h-px bg-gradient-to-r from-white/30 to-transparent w-16" />
    </motion.div>
  )
}

// Scampering puppy animation component
function ScamperingPuppy() {
  const [windowWidth, setWindowWidth] = useState(1200)

  useEffect(() => {
    setWindowWidth(window.innerWidth)
    const handleResize = () => setWindowWidth(window.innerWidth)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <motion.div
      className="absolute bottom-4 text-4xl pointer-events-none"
      initial={{ x: -100, opacity: 0 }}
      animate={{
        x: [null, windowWidth + 100],
        opacity: [0, 1, 1, 0]
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        repeatDelay: 15,
        ease: "linear"
      }}
      style={{ zIndex: 5 }}
    >
      🐕‍🦺
    </motion.div>
  )
}

export function Footer() {
  const [email, setEmail] = useState("")
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [currentPetIndex, setCurrentPetIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
    // Rotate through adopted pets feed
    const interval = setInterval(() => {
      setCurrentPetIndex((prev) => (prev + 1) % adoptedPetsData.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubscribed(true)
      setEmail("")
      setTimeout(() => setIsSubscribed(false), 3000)
    }
  }

  return (
    <footer className="relative bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white overflow-hidden" role="contentinfo" aria-label="Site footer">
      {/* Whimsical background elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 25c-2.5 0-4.5-2-4.5-4.5S27.5 16 30 16s4.5 2 4.5 4.5S32.5 25 30 25zm-8-8c-1.5 0-2.5-1-2.5-2.5S20.5 12 22 12s2.5 1 2.5 2.5S23.5 17 22 17zm16 0c-1.5 0-2.5-1-2.5-2.5S36.5 12 38 12s2.5 1 2.5 2.5S39.5 17 38 17zm-16 8c-1.5 0-2.5-1-2.5-2.5S20.5 20 22 20s2.5 1 2.5 2.5S23.5 25 22 25zm16 0c-1.5 0-2.5-1-2.5-2.5S36.5 20 38 20s2.5 1 2.5 2.5S39.5 25 38 25z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      {/* Scampering puppy animation */}
      {isLoaded && <ScamperingPuppy />}

      <div className="container mx-auto px-4 py-16 relative z-10">
        {/* Newsletter Signup Section - Featured at top */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="max-w-4xl mx-auto">
            <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
              <div className="flex items-center justify-center gap-3 mb-6">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Mail className="h-8 w-8 text-yellow-400" />
                </motion.div>
                <h2 className="text-3xl md:text-4xl font-bold text-white">
                  Join Our Pack! 🐾
                </h2>
              </div>

              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Get heartwarming adoption stories, pet care tips, and be the first to meet our newest rescues looking for homes!
              </p>

              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
                <div className="flex-1 relative">
                  <Input
                    type="email"
                    placeholder="Enter your email to spread the love"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full h-14 pl-6 pr-4 text-lg bg-white/90 border-0 rounded-2xl placeholder:text-gray-500 focus:ring-2 focus:ring-yellow-400"
                    required
                    aria-label="Email address for newsletter signup"
                  />
                </div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    type="submit"
                    size="lg"
                    className="h-14 px-8 bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-gray-900 font-bold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    disabled={isSubscribed}
                  >
                    <AnimatePresence mode="wait">
                      {isSubscribed ? (
                        <motion.div
                          key="success"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                          className="flex items-center gap-2"
                        >
                          <CheckCircle className="h-5 w-5" />
                          Welcome!
                        </motion.div>
                      ) : (
                        <motion.div
                          key="default"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                          className="flex items-center gap-2"
                        >
                          <Send className="h-5 w-5" />
                          Join Us
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Button>
                </motion.div>
              </form>

              <p className="text-sm text-blue-200 mt-4">
                🎉 Join 10,000+ pet lovers • No spam, just love • Unsubscribe anytime
              </p>
            </div>
          </div>
        </motion.div>

        {/* Paw-shaped divider */}
        <div className="flex justify-center mb-16">
          <PawDivider />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand and Mission */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="space-y-6"
          >
            <div className="flex items-center space-x-3">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Heart className="h-10 w-10 text-red-400" />
              </motion.div>
              <span className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-pink-400 bg-clip-text text-transparent">
                PetAdopt
              </span>
            </div>

            <p className="text-blue-100 leading-relaxed">
              🌟 Connecting loving families with pets in need. Every adoption saves a life and makes room for another rescue to find their forever home.
            </p>

            <div className="space-y-3">
              <h4 className="text-lg font-semibold text-white flex items-center gap-2">
                <Users className="h-5 w-5 text-yellow-400" />
                Follow Our Journey
              </h4>
              <div className="flex space-x-4">
                {[
                  { icon: Facebook, label: "Facebook", color: "hover:text-blue-400" },
                  { icon: Twitter, label: "Twitter", color: "hover:text-sky-400" },
                  { icon: Instagram, label: "Instagram", color: "hover:text-pink-400" }
                ].map(({ icon: Icon, label, color }) => (
                  <motion.a
                    key={label}
                    href="#"
                    className={cn("text-blue-200 transition-colors duration-300", color)}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    aria-label={`Follow us on ${label}`}
                  >
                    <Icon className="h-6 w-6" />
                  </motion.a>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Adoption Resources */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h3 className="text-xl font-bold text-white flex items-center gap-2">
              <PawPrint className="h-6 w-6 text-yellow-400" />
              Adoption Hub
            </h3>
            <ul className="space-y-3">
              {[
                { href: "/pets", label: "Find Your Perfect Pet", icon: "🐕", description: "Browse available pets" },
                { href: "/adoption-process", label: "Adoption Process", icon: "📋", description: "Step-by-step guide" },
                { href: "/adoption-stories", label: "Success Stories", icon: "💕", description: "Heartwarming tales" },
                { href: "/schedule-visit", label: "Schedule a Visit", icon: "📅", description: "Meet your future pet" }
              ].map((item, index) => (
                <motion.li
                  key={item.href}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="group flex items-start gap-3 text-blue-100 hover:text-white transition-all duration-300 p-2 rounded-lg hover:bg-white/20"
                  >
                    <span className="text-lg group-hover:scale-110 transition-transform duration-300">
                      {item.icon}
                    </span>
                    <div>
                      <div className="font-medium group-hover:text-yellow-400 transition-colors">
                        {item.label}
                      </div>
                      <div className="text-xs text-blue-200 opacity-75">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Get Involved */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="space-y-6"
          >
            <h3 className="text-xl font-bold text-white flex items-center gap-2">
              <Gift className="h-6 w-6 text-pink-400" />
              Get Involved
            </h3>
            <ul className="space-y-3">
              {[
                { href: "/volunteer", label: "Volunteer", icon: "🙋‍♀️", description: "Help save lives", badge: "Popular" },
                { href: "/foster", label: "Foster a Pet", icon: "🏠", description: "Temporary loving homes" },
                { href: "/donate", label: "Donate", icon: "💝", description: "Support our mission" },
                { href: "/events", label: "Events", icon: "🎉", description: "Join our community" }
              ].map((item, index) => (
                <motion.li
                  key={item.href}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="group flex items-start gap-3 text-blue-100 hover:text-white transition-all duration-300 p-2 rounded-lg hover:bg-white/20"
                  >
                    <span className="text-lg group-hover:scale-110 transition-transform duration-300">
                      {item.icon}
                    </span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium group-hover:text-pink-400 transition-colors">
                          {item.label}
                        </span>
                        {item.badge && (
                          <Badge className="bg-yellow-400 text-gray-900 text-xs px-2 py-0.5">
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-blue-200 opacity-75">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Happy Moments Feed */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-6"
          >
            <h3 className="text-xl font-bold text-white flex items-center gap-2">
              <Sparkles className="h-6 w-6 text-yellow-400" />
              Happy Moments
            </h3>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPetIndex}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-3"
                >
                  <div className="flex items-start gap-3">
                    <div className="text-3xl">
                      {adoptedPetsData[currentPetIndex].image}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold text-white">
                          {adoptedPetsData[currentPetIndex].name}
                        </span>
                        <span className="text-xs text-blue-200">
                          with {adoptedPetsData[currentPetIndex].adopter}
                        </span>
                      </div>
                      <p className="text-sm text-blue-100 leading-relaxed">
                        {adoptedPetsData[currentPetIndex].message}
                      </p>
                      <div className="flex items-center gap-2 mt-2 text-xs text-blue-200">
                        <MapPin className="h-3 w-3" />
                        <span>{adoptedPetsData[currentPetIndex].location}</span>
                        <span>•</span>
                        <span>{adoptedPetsData[currentPetIndex].timeAgo}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>

              <div className="flex justify-center gap-2 mt-4" role="tablist" aria-label="Adopted pets stories">
                {adoptedPetsData.map((pet, index) => (
                  <motion.button
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-400",
                      index === currentPetIndex ? "bg-yellow-400" : "bg-white/30"
                    )}
                    whileHover={{ scale: 1.2 }}
                    onClick={() => setCurrentPetIndex(index)}
                    role="tab"
                    aria-selected={index === currentPetIndex}
                    aria-label={`View ${pet.name}'s story`}
                  />
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-lg font-semibold text-white flex items-center gap-2">
                <Phone className="h-5 w-5 text-green-400" />
                Contact Us
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2 text-blue-100">
                  <MapPin className="h-4 w-4 text-green-400" />
                  <span>123 Pet Rescue Lane, Adoption City, AC 12345</span>
                </div>
                <div className="flex items-center gap-2 text-blue-100">
                  <Phone className="h-4 w-4 text-green-400" />
                  <span>(555) 123-PETS</span>
                </div>
                <div className="flex items-center gap-2 text-blue-100">
                  <Mail className="h-4 w-4 text-green-400" />
                  <span><EMAIL></span>
                </div>
              </div>

              <div className="pt-3 border-t border-white/20">
                <p className="text-sm font-medium text-red-400 flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Emergency Animal Services
                </p>
                <p className="text-sm text-blue-100">(555) 911-PETS • Available 24/7</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Paw-shaped divider */}
        <div className="flex justify-center my-16">
          <PawDivider />
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="pt-8 border-t border-white/20"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-center md:text-left">
              <p className="text-blue-100 font-medium">
                © 2024 PetAdopt • Made with 💕 for our furry friends
              </p>
              <p className="text-sm text-blue-200 opacity-75">
                501(c)(3) Non-Profit Organization • Tax ID: 12-3456789
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-6 text-sm">
              {[
                { href: "/privacy", label: "Privacy Policy" },
                { href: "/terms", label: "Terms of Service" },
                { href: "/accessibility", label: "Accessibility" }
              ].map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-blue-200 hover:text-white transition-colors duration-300 hover:underline"
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>

          <div className="text-center mt-6 pt-6 border-t border-white/10">
            <p className="text-blue-200 text-sm">
              🌟 Every adoption saves two lives - the pet you adopt and the one that takes their place 🌟
            </p>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}


