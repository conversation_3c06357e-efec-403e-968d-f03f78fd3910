"use client"

import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, User, Menu, Search, Shield, Settings, ChevronDown } from "lucide-react"
import { useState, useRef, useEffect } from "react"
import { UserRole } from "@prisma/client"
import { PermissionManager, Permission } from "@/lib/permissions"
import { SearchBar } from "./search-bar"
import {
  NavDropdown,
  adoptAndCareDropdownItems,
  petCareAndHealthDropdownItems,
  protectionAndInsuranceDropdownItems,
  communityAndResourcesDropdownItems
} from "./nav-dropdown"
import { MobileMenu } from "./mobile-menu"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { useNavigation } from "@/hooks/use-navigation"

export function Header() {
  const { data: session, status } = useSession()
  const navigation = useNavigation()
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  // Helper function to check if user has admin/staff access
  const hasAdminAccess = () => {
    return session?.user?.role && [UserRole.ADMIN, UserRole.STAFF].includes(session.user.role as UserRole)
  }

  // Helper function to check if user is admin
  const isAdmin = () => {
    return session?.user?.role === UserRole.ADMIN
  }

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div>
      <motion.header
        className={cn(
          "sticky top-0 z-50 w-full border-b transition-all duration-300",
          navigation.scrolled
            ? "bg-white dark:bg-gray-900 backdrop-blur-md shadow-lg border-gray-200 dark:border-gray-700"
            : "bg-white dark:bg-gray-900 backdrop-blur-sm border-gray-100 dark:border-gray-800"
        )}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex h-16 items-center justify-between relative">
            {/* Logo */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/" className="flex items-center space-x-2 group">
                <motion.div
                  whileHover={{ rotate: 10 }}
                  transition={{ duration: 0.2 }}
                >
                  <Heart className="h-8 w-8 text-primary group-hover:text-primary/80 transition-colors" />
                </motion.div>
                <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  PetAdopt
                </span>
              </Link>
            </motion.div>

            {/* Desktop Navigation - 4 Main Categories */}
            <nav className="hidden lg:flex items-center space-x-1" role="navigation" aria-label="Main navigation">
              <NavDropdown
                title="Adopt & Care"
                items={adoptAndCareDropdownItems}
                className="px-4 py-2 text-sm font-medium"
              />
              <NavDropdown
                title="Pet Health"
                items={petCareAndHealthDropdownItems}
                className="px-4 py-2 text-sm font-medium"
              />
              <NavDropdown
                title="Protection & Insurance"
                items={protectionAndInsuranceDropdownItems}
                className="px-4 py-2 text-sm font-medium"
              />
              <NavDropdown
                title="Community & Resources"
                items={communityAndResourcesDropdownItems}
                className="px-4 py-2 text-sm font-medium"
              />
              <Link href="/lost-pet/report">
                <Button
                  size="sm"
                  className="ml-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Report Lost Pet
                </Button>
              </Link>
            </nav>

            {/* Search and User Actions */}
            <div className="flex items-center space-x-3">
              {/* Desktop Search */}
              <div className="hidden md:block">
                <SearchBar
                  className="w-64 lg:w-80"
                  placeholder="Search pets, breeds..."
                />
              </div>

              {/* User Authentication */}
              {status === "loading" ? (
                <motion.div
                  className="h-8 w-8 bg-gray-200 rounded-full"
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                />
              ) : session ? (
                <div className="flex items-center space-x-2">
                  {/* Favorites */}
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                    <Link href="/favorites">
                      <Button variant="ghost" size="icon" className="relative">
                        <Heart className="h-4 w-4" />
                      </Button>
                    </Link>
                  </motion.div>

                  {/* User Menu */}
                  <div className="relative" ref={userMenuRef}>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setUserMenuOpen(!userMenuOpen)}
                        className={cn(
                          "transition-colors duration-200",
                          userMenuOpen && "bg-gray-100"
                        )}
                        aria-expanded={userMenuOpen}
                        aria-haspopup="true"
                      >
                        <User className="h-4 w-4" />
                      </Button>
                    </motion.div>

                    <AnimatePresence>
                      {userMenuOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: -10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -10, scale: 0.95 }}
                          transition={{ duration: 0.15 }}
                          className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50"
                          role="menu"
                        >
                          {/* User Info */}
                          <div className="px-4 py-3 border-b border-gray-100">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-primary" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 text-sm">
                                  {session.user?.name || session.user?.email}
                                </div>
                                <div className="text-xs text-gray-500 capitalize">
                                  {session.user?.role?.toLowerCase()}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Menu Items */}
                          <div className="py-1">
                            <Link
                              href="/dashboard"
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                              onClick={() => setUserMenuOpen(false)}
                              role="menuitem"
                            >
                              <User className="h-4 w-4 mr-3 text-gray-400" />
                              Dashboard
                            </Link>
                            <Link
                              href="/profile"
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                              onClick={() => setUserMenuOpen(false)}
                              role="menuitem"
                            >
                              <Settings className="h-4 w-4 mr-3 text-gray-400" />
                              Profile
                            </Link>
                            <Link
                              href="/applications"
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                              onClick={() => setUserMenuOpen(false)}
                              role="menuitem"
                            >
                              <Heart className="h-4 w-4 mr-3 text-gray-400" />
                              My Applications
                            </Link>

                            {/* Admin/Staff Access */}
                            {hasAdminAccess() && (
                              <div>
                                <div className="border-t border-gray-100 my-1" />
                                <Link
                                  href="/admin"
                                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                  onClick={() => setUserMenuOpen(false)}
                                  role="menuitem"
                                >
                                  <Shield className="h-4 w-4 mr-3 text-gray-400" />
                                  Admin Panel
                                </Link>
                                {isAdmin() && (
                                  <Link
                                    href="/admin/users"
                                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                    onClick={() => setUserMenuOpen(false)}
                                    role="menuitem"
                                  >
                                    <Settings className="h-4 w-4 mr-3 text-gray-400" />
                                    User Management
                                  </Link>
                                )}
                              </div>
                            )}

                            <div className="border-t border-gray-100 my-1" />
                            <button
                              onClick={() => {
                                signOut()
                                setUserMenuOpen(false)
                              }}
                              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors text-left"
                              role="menuitem"
                            >
                              <Settings className="h-4 w-4 mr-3 text-gray-400" />
                              Sign Out
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link href="/auth/signin">
                      <Button variant="ghost" size="sm">Sign In</Button>
                    </Link>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link href="/auth/signup">
                      <Button size="sm" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                        Sign Up
                      </Button>
                    </Link>
                  </motion.div>
                </div>
              )}

              {/* Mobile Menu Button */}
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button
                  variant="ghost"
                  size="icon"
                  className="lg:hidden"
                  onClick={navigation.toggleMobileMenu}
                  aria-expanded={navigation.isMobileMenuOpen}
                  aria-label="Toggle mobile menu"
                >
                  <motion.div
                    animate={{ rotate: navigation.isMobileMenuOpen ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-5 w-5" />
                  </motion.div>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={navigation.isMobileMenuOpen}
        onClose={navigation.closeMobileMenu}
      />
    </div>
  )
}
