"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Loader2, Heart, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

interface SparklingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline"
  size?: "sm" | "md" | "lg"
  loading?: boolean
  sparkleOnHover?: boolean
  children: React.ReactNode
}

// Sparkle animation component
const SparkleAnimation = ({ show }: { show: boolean }) => (
  <AnimatePresence>
    {show && (
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-yellow-300"
            style={{
              left: `${10 + (i % 4) * 25}%`,
              top: `${20 + Math.floor(i / 4) * 60}%`,
            }}
            initial={{ opacity: 0, scale: 0, rotate: 0 }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
              rotate: [0, 180, 360],
              x: [0, (i % 2 ? 10 : -10)],
              y: [0, (i % 2 ? -10 : 10)],
            }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{
              duration: 1.2,
              delay: i * 0.1,
              ease: "easeOut",
            }}
          >
            ✨
          </motion.div>
        ))}
      </div>
    )}
  </AnimatePresence>
)

// Floating hearts on click
const FloatingHearts = ({ show }: { show: boolean }) => (
  <AnimatePresence>
    {show && (
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-pink-400"
            style={{
              left: `${30 + i * 10}%`,
              top: "50%",
            }}
            initial={{ opacity: 0, scale: 0, y: 0 }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
              y: [0, -30, -60],
              x: [0, (i % 2 ? 15 : -15)],
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 1.5,
              delay: i * 0.1,
              ease: "easeOut",
            }}
          >
            💕
          </motion.div>
        ))}
      </div>
    )}
  </AnimatePresence>
)

// Paw print trail animation
const PawTrail = ({ show }: { show: boolean }) => (
  <AnimatePresence>
    {show && (
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-orange-300 text-sm"
            style={{
              left: `${20 + i * 20}%`,
              bottom: "10px",
            }}
            initial={{ opacity: 0, scale: 0, y: 10 }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0, 1, 0],
              y: [10, 0, -10],
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 1,
              delay: i * 0.15,
              ease: "easeOut",
            }}
          >
            🐾
          </motion.div>
        ))}
      </div>
    )}
  </AnimatePresence>
)

export function SparklingButton({
  variant = "primary",
  size = "md",
  loading = false,
  sparkleOnHover = true,
  children,
  className,
  onClick,
  ...props
}: SparklingButtonProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isClicked, setIsClicked] = useState(false)
  const [showHearts, setShowHearts] = useState(false)

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (loading) return
    
    setIsClicked(true)
    setShowHearts(true)
    
    setTimeout(() => {
      setIsClicked(false)
      setShowHearts(false)
    }, 1500)
    
    onClick?.(e)
  }

  const baseClasses = cn(
    "relative overflow-hidden font-semibold rounded-2xl transition-all duration-300",
    "focus:outline-none focus:ring-4 focus:ring-offset-2",
    "disabled:opacity-50 disabled:cursor-not-allowed",
    "transform-gpu", // Enable hardware acceleration
  )

  const variantClasses = {
    primary: cn(
      "bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500",
      "hover:from-pink-600 hover:via-purple-600 hover:to-blue-600",
      "text-white shadow-lg hover:shadow-xl",
      "focus:ring-pink-300",
    ),
    secondary: cn(
      "bg-gradient-to-r from-orange-400 via-yellow-400 to-green-400",
      "hover:from-orange-500 hover:via-yellow-500 hover:to-green-500",
      "text-white shadow-lg hover:shadow-xl",
      "focus:ring-orange-300",
    ),
    outline: cn(
      "bg-white dark:bg-gray-800 backdrop-blur-sm border-2 border-pink-300 dark:border-pink-400",
      "hover:border-pink-400 hover:bg-gray-50 dark:hover:bg-gray-700",
      "text-pink-600 hover:text-pink-700 dark:text-pink-400 dark:hover:text-pink-300",
      "focus:ring-pink-200",
    ),
  }

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg",
  }

  return (
    <motion.button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
      disabled={loading}
      {...props}
    >
      {/* Background glow effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 opacity-0"
        animate={isHovered ? { opacity: 0.3 } : { opacity: 0 }}
        transition={{ duration: 0.3 }}
      />

      {/* Sparkle animations */}
      {sparkleOnHover && <SparkleAnimation show={isHovered && !loading} />}
      
      {/* Floating hearts on click */}
      <FloatingHearts show={showHearts} />
      
      {/* Paw trail on hover */}
      <PawTrail show={isHovered && !loading} />

      {/* Button content */}
      <span className="relative z-10 flex items-center justify-center space-x-2">
        {loading ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading...</span>
          </>
        ) : (
          <>
            {variant === "primary" && (
              <motion.div
                animate={isHovered ? { rotate: [0, 10, -10, 0] } : {}}
                transition={{ duration: 0.6, repeat: isHovered ? Infinity : 0 }}
              >
                <Heart className="h-5 w-5" />
              </motion.div>
            )}
            <span>{children}</span>
            {sparkleOnHover && (
              <motion.div
                animate={isHovered ? { rotate: 360 } : {}}
                transition={{ duration: 1, repeat: isHovered ? Infinity : 0 }}
              >
                <Sparkles className="h-4 w-4" />
              </motion.div>
            )}
          </>
        )}
      </span>

      {/* Ripple effect on click */}
      <AnimatePresence>
        {isClicked && (
          <motion.div
            className="absolute inset-0 bg-white/30 rounded-2xl"
            initial={{ scale: 0, opacity: 0.8 }}
            animate={{ scale: 2, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6 }}
          />
        )}
      </AnimatePresence>
    </motion.button>
  )
}
