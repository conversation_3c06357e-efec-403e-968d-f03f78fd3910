"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { UserRole } from "@prisma/client"
import {
  Database,
  Download,
  Upload,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  HardDrive,
  Activity,
  Clock,
  BarChart3,
  Table,
  Zap
} from "lucide-react"
import { toast } from "sonner"
import { format } from "date-fns"

interface TableStat {
  table: string
  count: number
  error: string | null
}

interface DatabaseOverview {
  tableStats: TableStat[]
  recentActivity: {
    newUsers: number
    newApplications: number
    newAdoptions: number
    auditLogs: number
  }
  lastUpdated: string
}

interface DatabaseStats {
  dbInfo: Array<{
    database_name: string
    database_size: string
    postgres_version: string
  }>
  tableSizes: Array<{
    table_name: string
    size_pretty: string
    row_count: number
  }>
  indexUsage: Array<{
    tablename: string
    indexname: string
    idx_scan: number
    idx_tup_read: number
  }>
  error?: string
}

interface DatabaseHealth {
  status: string
  connectionTest: any[]
  slowQueries: Array<{
    query: string
    calls: number
    total_time: number
    mean_time: number
    rows: number
  }>
  activeConnections: Array<{
    active_connections: number
    longest_query_time: string | null
  }>
  lastChecked: string
  error?: string
}

interface BackupHistory {
  backups: Array<{
    id: string
    filename: string
    size: number
    status: string
    createdAt: string
    createdBy: {
      name: string
      email: string
    }
  }>
  error?: string
}

export default function DatabaseManagement() {
  const [overview, setOverview] = useState<DatabaseOverview | null>(null)
  const [stats, setStats] = useState<DatabaseStats | null>(null)
  const [health, setHealth] = useState<DatabaseHealth | null>(null)
  const [backups, setBackups] = useState<BackupHistory | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  const fetchDatabaseData = async (action: string = "") => {
    try {
      setLoading(true)
      const url = action ? `/api/admin/database?action=${action}` : '/api/admin/database'
      const response = await fetch(url)

      if (response.ok) {
        const data = await response.json()

        switch (action) {
          case "stats":
            setStats(data)
            break
          case "health":
            setHealth(data)
            break
          case "backups":
            setBackups(data)
            break
          default:
            setOverview(data)
            break
        }
      } else {
        toast.error(`Failed to load database ${action || 'overview'}`)
      }
    } catch (error) {
      console.error(`Error fetching database ${action}:`, error)
      toast.error(`Failed to load database ${action || 'overview'}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDatabaseData()
  }, [])

  useEffect(() => {
    if (activeTab !== "overview") {
      fetchDatabaseData(activeTab)
    }
  }, [activeTab])

  const handleCreateBackup = async () => {
    try {
      const response = await fetch('/api/admin/database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'backup' })
      })

      if (response.ok) {
        toast.success("Backup created successfully")
        fetchDatabaseData("backups")
      } else {
        toast.error("Failed to create backup")
      }
    } catch (error) {
      console.error("Error creating backup:", error)
      toast.error("Failed to create backup")
    }
  }

  const handleRunMaintenance = async () => {
    try {
      const response = await fetch('/api/admin/database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'maintenance' })
      })

      if (response.ok) {
        toast.success("Maintenance completed successfully")
        fetchDatabaseData()
      } else {
        toast.error("Failed to run maintenance")
      }
    } catch (error) {
      console.error("Error running maintenance:", error)
      toast.error("Failed to run maintenance")
    }
  }

  const getHealthStatus = () => {
    if (!health) return { icon: Clock, color: "text-gray-500", text: "Unknown" }

    if (health.status === "healthy") {
      return { icon: CheckCircle, color: "text-green-500", text: "Healthy" }
    } else {
      return { icon: XCircle, color: "text-red-500", text: "Error" }
    }
  }

  return (
    <AdminLayout
      title="Database Management"
      description="Database maintenance, backups, and administration"
      requiredRoles={[UserRole.ADMIN]}
    >
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="health">Health</TabsTrigger>
            <TabsTrigger value="backups">Backups</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Database Overview</h2>
              <Button onClick={() => fetchDatabaseData()} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600">Loading database overview...</span>
              </div>
            ) : !overview ? (
              <Card>
                <CardContent className="p-6">
                  <div className="text-center py-8">
                    <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Database Data Available
                    </h3>
                    <p className="text-gray-500 mb-4">
                      Unable to load database overview. This could be due to:
                    </p>
                    <ul className="text-sm text-gray-500 text-left max-w-md mx-auto space-y-1">
                      <li>• Database connection issues</li>
                      <li>• Missing database tables</li>
                      <li>• Insufficient permissions</li>
                    </ul>
                    <Button
                      onClick={() => fetchDatabaseData()}
                      className="mt-4"
                      variant="outline"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Retry Connection
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : overview && (
              <>
                {/* Recent Activity Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center">
                        <Activity className="h-8 w-8 text-blue-500" />
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-600">New Users (24h)</p>
                          <p className="text-2xl font-bold">{overview.recentActivity.newUsers}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center">
                        <HardDrive className="h-8 w-8 text-green-500" />
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-600">New Applications (24h)</p>
                          <p className="text-2xl font-bold">{overview.recentActivity.newApplications}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center">
                        <CheckCircle className="h-8 w-8 text-purple-500" />
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-600">New Adoptions (24h)</p>
                          <p className="text-2xl font-bold">{overview.recentActivity.newAdoptions}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center">
                        <Activity className="h-8 w-8 text-orange-500" />
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-600">Audit Logs (24h)</p>
                          <p className="text-2xl font-bold">{overview.recentActivity.auditLogs}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Table Statistics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Table className="h-6 w-6 mr-2" />
                      Table Statistics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {overview.tableStats.map((table) => (
                        <div key={table.table} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium capitalize">{table.table.replace(/([A-Z])/g, ' $1').trim()}</h4>
                            {table.error ? (
                              <XCircle className="h-5 w-5 text-red-500" />
                            ) : (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                          </div>
                          <p className="text-2xl font-bold mt-2">
                            {table.error ? 'Error' : table.count.toLocaleString()}
                          </p>
                          {table.error && (
                            <p className="text-sm text-red-500 mt-1">{table.error}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <div className="text-sm text-gray-500">
                  Last updated: {format(new Date(overview.lastUpdated), 'MMM dd, yyyy HH:mm:ss')}
                </div>
              </>
            )}
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="stats" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Database Statistics</h2>
              <Button onClick={() => fetchDatabaseData("stats")} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600">Loading statistics...</span>
              </div>
            ) : stats ? (
              <div className="space-y-6">
                {/* Database Info */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Database className="h-6 w-6 mr-2" />
                      Database Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {stats.error ? (
                      <div className="text-center py-8">
                        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                        <p className="text-gray-600">{stats.error}</p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {stats.dbInfo.map((info, index) => (
                          <div key={index} className="p-4 border rounded-lg">
                            <h4 className="font-medium">Database</h4>
                            <p className="text-lg font-bold">{info.database_name}</p>
                            <p className="text-sm text-gray-500">Size: {info.database_size}</p>
                            <p className="text-sm text-gray-500">Version: {info.postgres_version}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Table Sizes */}
                {stats.tableSizes.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <BarChart3 className="h-6 w-6 mr-2" />
                        Table Sizes
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-3 px-4 font-medium">Table</th>
                              <th className="text-left py-3 px-4 font-medium">Size</th>
                              <th className="text-left py-3 px-4 font-medium">Rows</th>
                            </tr>
                          </thead>
                          <tbody>
                            {stats.tableSizes.map((table, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50">
                                <td className="py-3 px-4 font-medium">{table.table_name}</td>
                                <td className="py-3 px-4">{table.size_pretty}</td>
                                <td className="py-3 px-4">{table.row_count?.toLocaleString() || 'N/A'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Index Usage */}
                {stats.indexUsage.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Zap className="h-6 w-6 mr-2" />
                        Index Usage
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-3 px-4 font-medium">Table</th>
                              <th className="text-left py-3 px-4 font-medium">Index</th>
                              <th className="text-left py-3 px-4 font-medium">Scans</th>
                              <th className="text-left py-3 px-4 font-medium">Tuples Read</th>
                            </tr>
                          </thead>
                          <tbody>
                            {stats.indexUsage.map((index, i) => (
                              <tr key={i} className="border-b hover:bg-gray-50">
                                <td className="py-3 px-4">{index.tablename}</td>
                                <td className="py-3 px-4 font-mono text-sm">{index.indexname}</td>
                                <td className="py-3 px-4">{index.idx_scan?.toLocaleString() || 0}</td>
                                <td className="py-3 px-4">{index.idx_tup_read?.toLocaleString() || 0}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Database className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No statistics available</h3>
                <p className="text-gray-500">Click refresh to load database statistics.</p>
              </div>
            )}
          </TabsContent>

          {/* Health Tab */}
          <TabsContent value="health" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Database Health</h2>
              <Button onClick={() => fetchDatabaseData("health")} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Check Health
              </Button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600">Checking database health...</span>
              </div>
            ) : health ? (
              <div className="space-y-6">
                {/* Health Status */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="h-6 w-6 mr-2" />
                      Health Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-4">
                      {(() => {
                        const status = getHealthStatus()
                        const StatusIcon = status.icon
                        return (
                          <>
                            <StatusIcon className={`h-12 w-12 ${status.color}`} />
                            <div>
                              <h3 className="text-xl font-bold">{status.text}</h3>
                              <p className="text-gray-500">
                                Last checked: {format(new Date(health.lastChecked), 'MMM dd, yyyy HH:mm:ss')}
                              </p>
                              {health.error && (
                                <p className="text-red-500 text-sm mt-1">{health.error}</p>
                              )}
                            </div>
                          </>
                        )
                      })()}
                    </div>
                  </CardContent>
                </Card>

                {/* Active Connections */}
                {health.activeConnections && health.activeConnections.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Activity className="h-6 w-6 mr-2" />
                        Active Connections
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {health.activeConnections.map((conn, index) => (
                          <div key={index} className="p-4 border rounded-lg">
                            <h4 className="font-medium">Active Connections</h4>
                            <p className="text-2xl font-bold">{conn.active_connections}</p>
                            {conn.longest_query_time && (
                              <p className="text-sm text-gray-500">
                                Longest query: {conn.longest_query_time}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Slow Queries */}
                {health.slowQueries && health.slowQueries.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Clock className="h-6 w-6 mr-2" />
                        Slow Queries
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-3 px-4 font-medium">Query</th>
                              <th className="text-left py-3 px-4 font-medium">Calls</th>
                              <th className="text-left py-3 px-4 font-medium">Avg Time (ms)</th>
                              <th className="text-left py-3 px-4 font-medium">Total Time (ms)</th>
                            </tr>
                          </thead>
                          <tbody>
                            {health.slowQueries.map((query, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50">
                                <td className="py-3 px-4 font-mono text-sm max-w-xs truncate">
                                  {query.query}
                                </td>
                                <td className="py-3 px-4">{query.calls}</td>
                                <td className="py-3 px-4">{query.mean_time?.toFixed(2)}</td>
                                <td className="py-3 px-4">{query.total_time?.toFixed(2)}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Health check not available</h3>
                <p className="text-gray-500">Click "Check Health" to run a database health check.</p>
              </div>
            )}
          </TabsContent>

          {/* Backups Tab */}
          <TabsContent value="backups" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Database Backups</h2>
              <div className="flex space-x-2">
                <Button onClick={() => fetchDatabaseData("backups")} variant="outline" disabled={loading}>
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button onClick={handleCreateBackup}>
                  <Download className="h-4 w-4 mr-2" />
                  Create Backup
                </Button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Download className="h-8 w-8 text-blue-500" />
                    <div className="ml-4">
                      <h4 className="font-medium">Create Backup</h4>
                      <p className="text-sm text-gray-500">Full database backup</p>
                    </div>
                  </div>
                  <Button className="w-full mt-4" onClick={handleCreateBackup}>
                    Create Now
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Upload className="h-8 w-8 text-green-500" />
                    <div className="ml-4">
                      <h4 className="font-medium">Restore</h4>
                      <p className="text-sm text-gray-500">Restore from backup</p>
                    </div>
                  </div>
                  <Button className="w-full mt-4" variant="outline" disabled>
                    Restore (Soon)
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <RefreshCw className="h-8 w-8 text-orange-500" />
                    <div className="ml-4">
                      <h4 className="font-medium">Maintenance</h4>
                      <p className="text-sm text-gray-500">Optimize database</p>
                    </div>
                  </div>
                  <Button className="w-full mt-4" variant="outline" onClick={handleRunMaintenance}>
                    Run Maintenance
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                    <div className="ml-4">
                      <h4 className="font-medium">Monitor</h4>
                      <p className="text-sm text-gray-500">Health monitoring</p>
                    </div>
                  </div>
                  <Button className="w-full mt-4" variant="outline" onClick={() => setActiveTab("health")}>
                    View Health
                  </Button>
                </CardContent>
              </Card>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600">Loading backup history...</span>
              </div>
            ) : backups && backups.backups.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-6 w-6 mr-2" />
                    Backup History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4 font-medium">Filename</th>
                          <th className="text-left py-3 px-4 font-medium">Size</th>
                          <th className="text-left py-3 px-4 font-medium">Status</th>
                          <th className="text-left py-3 px-4 font-medium">Created By</th>
                          <th className="text-left py-3 px-4 font-medium">Created At</th>
                          <th className="text-left py-3 px-4 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {backups.backups.map((backup) => (
                          <tr key={backup.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4 font-mono text-sm">{backup.filename}</td>
                            <td className="py-3 px-4">{(backup.size / 1024 / 1024).toFixed(2)} MB</td>
                            <td className="py-3 px-4">
                              <Badge
                                className={
                                  backup.status === 'completed'
                                    ? 'bg-green-100 text-green-800'
                                    : backup.status === 'failed'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }
                              >
                                {backup.status}
                              </Badge>
                            </td>
                            <td className="py-3 px-4">
                              <div>
                                <div className="text-sm font-medium">{backup.createdBy.name}</div>
                                <div className="text-sm text-gray-500">{backup.createdBy.email}</div>
                              </div>
                            </td>
                            <td className="py-3 px-4 text-sm">
                              {format(new Date(backup.createdAt), 'MMM dd, yyyy HH:mm')}
                            </td>
                            <td className="py-3 px-4">
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline">
                                  <Download className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" disabled>
                                  <Upload className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Database className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No backups found</h3>
                  <p className="text-gray-500 mb-6">Create your first database backup to get started.</p>
                  <Button onClick={handleCreateBackup}>
                    <Download className="h-4 w-4 mr-2" />
                    Create First Backup
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}