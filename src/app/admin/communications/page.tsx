"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { UserRole } from "@prisma/client"
import {
  MessageSquare,
  Mail,
  Bell,
  Send,
  Users,
  Search,
  Filter,
  RefreshCw,
  Plus,
  Eye,
  EyeOff,
  Trash2,
  CheckCircle,
  Clock,
  User
} from "lucide-react"
import { toast } from "sonner"
import { format } from "date-fns"

interface Message {
  id: string
  subject: string | null
  content: string
  messageType: string
  isRead: boolean
  createdAt: string
  readAt: string | null
  sender: {
    id: string
    name: string
    email: string
    role: string
  }
}

interface User {
  id: string
  name: string
  email: string
  phone: string | null
  role: string
  status: string
}

interface MessagesResponse {
  messages: Message[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function CommunicationsManagement() {
  const [activeTab, setActiveTab] = useState("compose")
  const [messages, setMessages] = useState<MessagesResponse | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])

  // Message form state
  const [messageForm, setMessageForm] = useState({
    subject: "",
    content: "",
    messageType: "general",
    sendEmail: false,
    sendSMS: false
  })

  // Filters for messages
  const [filters, setFilters] = useState({
    search: "",
    messageType: "",
    isRead: "",
    page: 1,
    limit: 10
  })

  const fetchMessages = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()

      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString())
      })

      const response = await fetch(`/api/admin/communications?${params}`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data)
      } else {
        toast.error("Failed to load messages")
      }
    } catch (error) {
      console.error("Error fetching messages:", error)
      toast.error("Failed to load messages")
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async (search: string = "", role: string = "") => {
    try {
      const params = new URLSearchParams()
      if (search) params.append("search", search)
      if (role) params.append("role", role)

      const response = await fetch(`/api/admin/communications?${params}`, {
        method: "PUT"
      })

      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
      } else {
        toast.error("Failed to load users")
      }
    } catch (error) {
      console.error("Error fetching users:", error)
      toast.error("Failed to load users")
    }
  }

  useEffect(() => {
    if (activeTab === "messages") {
      fetchMessages()
    } else if (activeTab === "compose") {
      fetchUsers()
    }
  }, [activeTab, filters.page])

  const handleSendMessage = async () => {
    if (!messageForm.content.trim()) {
      toast.error("Message content is required")
      return
    }

    if (selectedUsers.length === 0) {
      toast.error("Please select at least one recipient")
      return
    }

    try {
      setLoading(true)
      const response = await fetch("/api/admin/communications", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          recipientIds: selectedUsers,
          ...messageForm
        })
      })

      if (response.ok) {
        const data = await response.json()
        toast.success(`Message sent to ${data.sentCount} recipients`)
        setMessageForm({
          subject: "",
          content: "",
          messageType: "general",
          sendEmail: false,
          sendSMS: false
        })
        setSelectedUsers([])
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to send message")
      }
    } catch (error) {
      console.error("Error sending message:", error)
      toast.error("Failed to send message")
    } finally {
      setLoading(false)
    }
  }

  const handleMarkAsRead = async (messageIds: string[], markAsRead: boolean) => {
    try {
      const response = await fetch("/api/admin/communications", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ messageIds, markAsRead })
      })

      if (response.ok) {
        toast.success(`Messages marked as ${markAsRead ? 'read' : 'unread'}`)
        fetchMessages()
      } else {
        toast.error("Failed to update messages")
      }
    } catch (error) {
      console.error("Error updating messages:", error)
      toast.error("Failed to update messages")
    }
  }

  const getMessageTypeColor = (type: string) => {
    const colors = {
      'general': 'bg-blue-100 text-blue-800',
      'adoption': 'bg-green-100 text-green-800',
      'volunteer': 'bg-purple-100 text-purple-800',
      'donation': 'bg-yellow-100 text-yellow-800',
      'emergency': 'bg-red-100 text-red-800'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  return (
    <AdminLayout
      title="Communications"
      description="Send notifications, emails, and messages"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="compose">Compose Message</TabsTrigger>
            <TabsTrigger value="messages">Message History</TabsTrigger>
          </TabsList>

          {/* Compose Message Tab */}
          <TabsContent value="compose" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Message Composition */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Send className="h-6 w-6 mr-2" />
                    Compose Message
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="subject">Subject (Optional)</Label>
                    <Input
                      id="subject"
                      placeholder="Enter message subject..."
                      value={messageForm.subject}
                      onChange={(e) => setMessageForm(prev => ({ ...prev, subject: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="messageType">Message Type</Label>
                    <Select
                      value={messageForm.messageType}
                      onValueChange={(value) => setMessageForm(prev => ({ ...prev, messageType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select message type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General</SelectItem>
                        <SelectItem value="adoption">Adoption</SelectItem>
                        <SelectItem value="volunteer">Volunteer</SelectItem>
                        <SelectItem value="donation">Donation</SelectItem>
                        <SelectItem value="emergency">Emergency</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="content">Message Content *</Label>
                    <Textarea
                      id="content"
                      placeholder="Enter your message..."
                      rows={6}
                      value={messageForm.content}
                      onChange={(e) => setMessageForm(prev => ({ ...prev, content: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Delivery Options</Label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sendEmail"
                          checked={messageForm.sendEmail}
                          onCheckedChange={(checked) =>
                            setMessageForm(prev => ({ ...prev, sendEmail: checked as boolean }))
                          }
                        />
                        <Label htmlFor="sendEmail" className="flex items-center">
                          <Mail className="h-4 w-4 mr-1" />
                          Send Email
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sendSMS"
                          checked={messageForm.sendSMS}
                          onCheckedChange={(checked) =>
                            setMessageForm(prev => ({ ...prev, sendSMS: checked as boolean }))
                          }
                        />
                        <Label htmlFor="sendSMS" className="flex items-center">
                          <Bell className="h-4 w-4 mr-1" />
                          Send SMS
                        </Label>
                      </div>
                    </div>
                  </div>

                  <Button
                    onClick={handleSendMessage}
                    disabled={loading || !messageForm.content.trim() || selectedUsers.length === 0}
                    className="w-full"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Send Message ({selectedUsers.length} recipients)
                  </Button>
                </CardContent>
              </Card>

              {/* Recipient Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-6 w-6 mr-2" />
                    Select Recipients
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Search users..."
                      onChange={(e) => fetchUsers(e.target.value)}
                    />
                    <Select onValueChange={(value) => fetchUsers("", value)}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Roles</SelectItem>
                        <SelectItem value="USER">Users</SelectItem>
                        <SelectItem value="VOLUNTEER">Volunteers</SelectItem>
                        <SelectItem value="STAFF">Staff</SelectItem>
                        <SelectItem value="ADMIN">Admins</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {users.map((user) => (
                      <div key={user.id} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          id={user.id}
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedUsers(prev => [...prev, user.id])
                            } else {
                              setSelectedUsers(prev => prev.filter(id => id !== user.id))
                            }
                          }}
                        />
                        <div className="flex-1">
                          <div className="text-sm font-medium">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {user.role}
                            </Badge>
                            {user.phone && (
                              <Badge variant="outline" className="text-xs">
                                <Bell className="h-3 w-3 mr-1" />
                                SMS
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {selectedUsers.length > 0 && (
                    <div className="pt-2 border-t">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">
                          {selectedUsers.length} recipients selected
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedUsers([])}
                        >
                          Clear All
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Message History Tab */}
          <TabsContent value="messages" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Message History</h2>
              <Button onClick={fetchMessages} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="h-5 w-5 mr-2" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="search">Search</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="search"
                        placeholder="Search messages..."
                        value={filters.search}
                        onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="messageType">Message Type</Label>
                    <Select
                      value={filters.messageType}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, messageType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All types</SelectItem>
                        <SelectItem value="general">General</SelectItem>
                        <SelectItem value="adoption">Adoption</SelectItem>
                        <SelectItem value="volunteer">Volunteer</SelectItem>
                        <SelectItem value="donation">Donation</SelectItem>
                        <SelectItem value="emergency">Emergency</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="isRead">Read Status</Label>
                    <Select
                      value={filters.isRead}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, isRead: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All messages" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All messages</SelectItem>
                        <SelectItem value="true">Read</SelectItem>
                        <SelectItem value="false">Unread</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-end space-x-2">
                    <Button onClick={fetchMessages} className="flex-1">
                      <Search className="h-4 w-4 mr-2" />
                      Search
                    </Button>
                    <Button variant="outline" onClick={() => setFilters({
                      search: "", messageType: "", isRead: "", page: 1, limit: 10
                    })}>
                      Clear
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Messages List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-6 w-6 mr-2" />
                  Messages
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-600">Loading messages...</span>
                  </div>
                ) : messages?.messages.length === 0 ? (
                  <div className="text-center py-12">
                    <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
                    <p className="text-gray-500">Try adjusting your filters or compose a new message.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      {messages?.messages.map((message) => (
                        <div
                          key={message.id}
                          className={`p-4 border rounded-lg ${message.isRead ? 'bg-gray-50' : 'bg-white border-blue-200'}`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <Badge className={getMessageTypeColor(message.messageType)}>
                                  {message.messageType.toUpperCase()}
                                </Badge>
                                {!message.isRead && (
                                  <Badge variant="outline" className="text-blue-600 border-blue-600">
                                    Unread
                                  </Badge>
                                )}
                              </div>

                              {message.subject && (
                                <h4 className="font-medium text-gray-900 mb-1">{message.subject}</h4>
                              )}

                              <p className="text-gray-700 mb-2">{message.content}</p>

                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <div className="flex items-center">
                                  <User className="h-4 w-4 mr-1" />
                                  {message.sender.name}
                                </div>
                                <div className="flex items-center">
                                  <Clock className="h-4 w-4 mr-1" />
                                  {format(new Date(message.createdAt), 'MMM dd, yyyy HH:mm')}
                                </div>
                                {message.readAt && (
                                  <div className="flex items-center">
                                    <CheckCircle className="h-4 w-4 mr-1" />
                                    Read {format(new Date(message.readAt), 'MMM dd, yyyy HH:mm')}
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="flex space-x-2 ml-4">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleMarkAsRead([message.id], !message.isRead)}
                              >
                                {message.isRead ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {messages && messages.pagination.totalPages > 1 && (
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-700">
                          Showing {((messages.pagination.page - 1) * messages.pagination.limit) + 1} to{' '}
                          {Math.min(messages.pagination.page * messages.pagination.limit, messages.pagination.total)} of{' '}
                          {messages.pagination.total} messages
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                            disabled={!messages.pagination.hasPrev}
                          >
                            Previous
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                            disabled={!messages.pagination.hasNext}
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}