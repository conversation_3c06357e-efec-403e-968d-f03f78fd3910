"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Shield, ArrowRight, Lock, Users, BarChart3, Settings } from "lucide-react"

export default function AdminPortalPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    // If user is already authenticated with admin role, redirect to dashboard
    if (status === "authenticated" && session?.user?.role === "ADMIN") {
      router.push("/admin")
    }
  }, [status, session, router])

  const handleAccessPortal = () => {
    if (status === "authenticated" && (session?.user?.role === "ADMIN" || session?.user?.role === "STAFF")) {
      router.push("/admin")
    } else {
      router.push("/admin/login")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <Shield className="w-8 h-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Pet Adoption Admin Portal</h1>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push("/")}
              className="text-gray-600 hover:text-gray-800"
            >
              ← Back to Main Site
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <div className="mx-auto w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mb-6">
            <Shield className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Administrative Access Portal
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Secure management interface for pet adoption platform administrators and staff members.
          </p>
        </div>

        {/* Access Card */}
        <div className="mb-12">
          <Card className="shadow-xl border-0 bg-white">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center space-x-2">
                <Lock className="w-6 h-6 text-blue-600" />
                <span>Secure Access Required</span>
              </CardTitle>
              <CardDescription className="text-gray-600">
                This portal is restricted to authorized personnel only
              </CardDescription>
            </CardHeader>
            
            <CardContent className="text-center">
              <Button
                onClick={handleAccessPortal}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-medium"
              >
                Access Admin Dashboard
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              
              <div className="mt-6 text-sm text-gray-500">
                <p>Authorized users will be redirected to the secure login page</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card className="border border-gray-200 hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <CardTitle className="text-lg">User Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-center">
                Manage user accounts, applications, and access permissions
              </p>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <BarChart3 className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <CardTitle className="text-lg">Analytics & Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-center">
                View adoption statistics, application metrics, and platform insights
              </p>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <Settings className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <CardTitle className="text-lg">System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-center">
                Configure platform settings, content management, and system preferences
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Security Notice */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-6 h-6 text-amber-600 mt-0.5" />
            <div>
              <h3 className="font-semibold text-amber-800 mb-2">Security Notice</h3>
              <p className="text-amber-700 text-sm">
                This administrative portal is protected by multi-layer security measures. 
                All access attempts are logged and monitored. Unauthorized access attempts 
                will be reported to system administrators.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
