"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  FileText,
  Star,
  Heart,
  Users,
  TrendingUp,
  Gift,
  CheckCircle,
  Calendar,
  Award,
  Shield
} from "lucide-react"
import { motion } from "framer-motion"

interface SurveyQuestion {
  id: string
  question: string
  type: "rating" | "text" | "textarea" | "select"
  options?: string[]
  required: boolean
}

const surveyQuestions: SurveyQuestion[] = [
  {
    id: "overall_satisfaction",
    question: "How satisfied are you with our pet adoption services?",
    type: "rating",
    required: true
  },
  {
    id: "adoption_process",
    question: "How would you rate the adoption process?",
    type: "rating",
    required: true
  },
  {
    id: "staff_helpfulness",
    question: "How helpful was our staff during your visit?",
    type: "rating",
    required: true
  },
  {
    id: "website_experience",
    question: "How was your experience using our website?",
    type: "rating",
    required: true
  },
  {
    id: "pet_matching",
    question: "How well did we help you find the right pet?",
    type: "rating",
    required: false
  },
  {
    id: "improvements",
    question: "What improvements would you suggest for our services?",
    type: "textarea",
    required: false
  },
  {
    id: "recommend",
    question: "Would you recommend our adoption center to others?",
    type: "select",
    options: ["Definitely", "Probably", "Maybe", "Probably not", "Definitely not"],
    required: true
  },
  {
    id: "additional_comments",
    question: "Any additional comments or feedback?",
    type: "textarea",
    required: false
  }
]

export default function ConsumerSurveyPage() {
  const [responses, setResponses] = useState<Record<string, string | number>>({})
  const [submitted, setSubmitted] = useState(false)
  const [currentYear] = useState(new Date().getFullYear())

  const handleRatingChange = (questionId: string, rating: number) => {
    setResponses(prev => ({ ...prev, [questionId]: rating }))
  }

  const handleInputChange = (questionId: string, value: string) => {
    setResponses(prev => ({ ...prev, [questionId]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the data to your backend
    console.log("Survey responses:", responses)
    setSubmitted(true)
  }

  const renderRatingInput = (questionId: string) => (
    <div className="flex space-x-2">
      {[1, 2, 3, 4, 5].map((rating) => (
        <button
          key={rating}
          type="button"
          onClick={() => handleRatingChange(questionId, rating)}
          className={`p-2 rounded-full transition-colors ${
            responses[questionId] >= rating
              ? 'text-yellow-500'
              : 'text-gray-300 hover:text-yellow-400'
          }`}
        >
          <Star className="w-6 h-6 fill-current" />
        </button>
      ))}
      <span className="ml-2 text-sm text-gray-600">
        {responses[questionId] ? `${responses[questionId]}/5` : 'Not rated'}
      </span>
    </div>
  )

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="max-w-md mx-auto"
        >
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl text-green-800">Thank You!</CardTitle>
              <CardDescription>
                Your feedback has been submitted successfully
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6">
                We appreciate you taking the time to share your thoughts. Your feedback helps us improve our services.
              </p>
              <Button onClick={() => window.location.href = '/'} className="w-full">
                Return to Home
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {currentYear} Consumer
              <span className="block text-blue-300">Survey</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-purple-100">
              Help us improve our pet adoption services with your valuable feedback
            </p>
            <Badge className="bg-white/20 text-white border-white/30">
              <Calendar className="w-4 h-4 mr-1" />
              Annual Survey {currentYear}
            </Badge>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Survey Benefits */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {[
            {
              icon: Heart,
              title: "Improve Services",
              description: "Your feedback helps us enhance our adoption process"
            },
            {
              icon: Users,
              title: "Better Experience",
              description: "Help us create better experiences for future adopters"
            },
            {
              icon: Award,
              title: "Make a Difference",
              description: "Contribute to improving pet welfare in our community"
            }
          ].map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Survey Form */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-6 h-6 mr-2 text-blue-600" />
              {currentYear} Pet Adoption Survey
            </CardTitle>
            <CardDescription>
              Please take a few minutes to share your experience with us. Your responses are anonymous and confidential.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-8">
              {surveyQuestions.map((question, index) => (
                <motion.div
                  key={question.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="space-y-3"
                >
                  <label className="text-base font-medium">
                    {question.question}
                    {question.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  
                  {question.type === "rating" && renderRatingInput(question.id)}
                  
                  {question.type === "text" && (
                    <Input
                      value={responses[question.id] || ""}
                      onChange={(e) => handleInputChange(question.id, e.target.value)}
                      required={question.required}
                    />
                  )}
                  
                  {question.type === "textarea" && (
                    <Textarea
                      value={responses[question.id] || ""}
                      onChange={(e) => handleInputChange(question.id, e.target.value)}
                      required={question.required}
                      rows={4}
                      placeholder="Please share your thoughts..."
                    />
                  )}
                  
                  {question.type === "select" && (
                    <select
                      value={responses[question.id] || ""}
                      onChange={(e) => handleInputChange(question.id, e.target.value)}
                      required={question.required}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Please select...</option>
                      {question.options?.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  )}
                </motion.div>
              ))}
              
              <div className="pt-6 border-t">
                <Button type="submit" className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  Submit Survey
                </Button>
                <p className="text-sm text-gray-500 text-center mt-2">
                  Thank you for helping us improve our services!
                </p>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Privacy Notice */}
        <Alert className="max-w-4xl mx-auto mt-8">
          <Shield className="h-4 w-4" />
          <AlertTitle>Privacy Notice</AlertTitle>
          <AlertDescription>
            Your responses are completely anonymous and will only be used to improve our services. 
            We do not collect any personal identifying information through this survey.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
