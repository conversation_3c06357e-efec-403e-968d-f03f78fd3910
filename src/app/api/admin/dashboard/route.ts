import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, PetStatus, ApplicationStatus, FosterStatus, VolunteerStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    console.log("Admin dashboard API accessed")
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      console.log("Dashboard access denied: No session")
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has admin/staff permissions
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      console.log(`Dashboard access denied: User ${session.user.email} has role ${session.user.role}`)
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    console.log(`Dashboard access granted: User ${session.user.email} with role ${session.user.role}`)

    // Get dashboard statistics
    const [
      totalPets,
      availablePets,
      pendingApplications,
      approvedApplications,
      totalVolunteers,
      activeVolunteers,
      totalFosters,
      activeFosters,
      recentApplications,
      recentPets,
    ] = await Promise.all([
      // Pet statistics
      prisma.pet.count(),
      prisma.pet.count({
        where: { status: PetStatus.AVAILABLE }
      }),
      
      // Application statistics
      prisma.application.count({
        where: {
          status: {
            in: [
              ApplicationStatus.SUBMITTED,
              ApplicationStatus.UNDER_REVIEW,
              ApplicationStatus.REFERENCE_CHECK,
              ApplicationStatus.HOME_VISIT_SCHEDULED,
              ApplicationStatus.HOME_VISIT_COMPLETED,
            ]
          }
        }
      }),
      prisma.application.count({
        where: { status: ApplicationStatus.APPROVED }
      }),
      
      // Volunteer statistics
      prisma.volunteerProfile.count(),
      prisma.volunteerProfile.count({
        where: { status: VolunteerStatus.ACTIVE }
      }),
      
      // Foster statistics
      prisma.fosterProfile.count(),
      prisma.fosterProfile.count({
        where: { status: FosterStatus.ACTIVE }
      }),
      
      // Recent applications
      prisma.application.findMany({
        take: 5,
        orderBy: { submittedAt: "desc" },
        include: {
          user: {
            select: {
              name: true,
              email: true,
            }
          },
          pet: {
            select: {
              name: true,
              species: true,
            }
          }
        }
      }),
      
      // Recent pets
      prisma.pet.findMany({
        take: 5,
        orderBy: { arrivalDate: "desc" },
        select: {
          id: true,
          name: true,
          species: true,
          breed: true,
          status: true,
          arrivalDate: true,
        }
      }),
    ])

    const dashboardStats = {
      totalPets,
      availablePets,
      pendingApplications,
      approvedApplications,
      totalVolunteers,
      activeVolunteers,
      totalFosters,
      activeFosters,
      recentApplications,
      recentPets,
    }

    return NextResponse.json(dashboardStats)

  } catch (error) {
    console.error("Error fetching dashboard stats:", error)
    return NextResponse.json(
      { error: "Failed to fetch dashboard statistics" },
      { status: 500 }
    )
  }
}
